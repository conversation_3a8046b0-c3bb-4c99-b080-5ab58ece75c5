<template>
  <div class="template-page">
    <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
      <template #before></template>

      <!-- 工具栏自定义按钮 -->
      <template #toolbar:after>
      </template>

      <!-- 状态列自定义渲染 -->
      <template #table:value3:simple="{ row }">
        <el-tag :type="getStatusTagType(row.value3)">
          {{ row.value3 }}
        </el-tag>
      </template>

      <!-- 操作列自定义渲染 -->
      <template #table:action:after="{ row }">
        <el-button type="text" size="mini" @click="handleAlgorithmList(row)">算法列表</el-button>
      </template>

      <template #info:before></template>
      <template #after></template>
    </EleSheet>

    <!-- 算法列表对话框 -->
    <AlgorithmListDialog
      ref="algorithmListDialog"
      @success="handleAlgorithmListSuccess"
    />
  </div>
</template>

<script>
import request from '@/utils/request.js'
import { templateStatus } from '@/dicts/video/index.js'
import AlgorithmListDialog from './components/AlgorithmListDialog.vue'
import dayjs from 'dayjs'

export default {
  name: 'VideoProcessTemplate',
  components: {
    AlgorithmListDialog
  },
  data() {
    return {
      tableType: 'template_orchestration_management',
      templateAlgorithms: {} // 存储每个模板的算法列表
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '处理模板',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 任务模板名称
          value1: {
            type: 'text',
            label: '任务模板名称',
            align: 'left',
            width: 200,
            search: {
              placeholder: '请输入任务模板名称'
            },
            form: {
              rules: true,
              placeholder: '请输入任务模板名称'
            }
          },
          // 数量
          value2: {
            label: '算法数量',
            width: 100,
            search: {
              hidden: true
            },
            form: {
              hidden: true,
              placeholder: '请输入数量',
              min: 0
            }
          },
          // 状态
          value3: {
            type: 'select',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...templateStatus
              ]
            },
            form: {
              value: '启用'
            },
            options: templateStatus
          },
          // 描述
          value4: {
            type: 'textarea',
            label: '描述',
            search: {
              hidden: true
            },
            form: {
              placeholder: '请输入模板描述',
              rows: 3
            }
          },
          // 创建人
          value50: {
            type: 'text',
            label: '创建人',
            width: 120,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            add: { value: 'admin' }
          },
          // 创建时间
          value51: {
            type: 'text',
            label: '创建时间',
            width: 160,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          }
        }
      }
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '启用': 'success',
        '停用': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 编辑模板
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },

    // 复制模板
    handleCopy(row) {
      this.$modal.confirm(`确认要复制模板"${row.value1}"吗？`).then(() => {
        const copyData = {
          ...row,
          value1: `${row.value1}_副本`,
          id: undefined // 清除ID，作为新记录添加
        }

        request({
          url: '/system/AutoOsmotic',
          method: 'post',
          data: {
            ...copyData,
            type: this.tableType
          }
        }).then(() => {
          this.$modal.msgSuccess('复制成功')
          this.$refs.sheetRef.getTableData()
        }).catch(() => {
          this.$modal.msgError('复制失败')
        })
      }).catch(() => {})
    },

    // 删除模板
    handleDelete(row) {
      this.$modal.confirm(`确认要删除模板"${row.value1}"吗？`).then(() => {
        request({
          url: `/system/AutoOsmotic/${row.id}`,
          method: 'delete'
        }).then(() => {
          this.$modal.msgSuccess('删除成功')
          this.$refs.sheetRef.getTableData()
        }).catch(() => {
          this.$modal.msgError('删除失败')
        })
      }).catch(() => {})
    },

    // 批量导入
    handleImport() {
      this.$modal.msgInfo('批量导入功能开发中...')
    },

    // 批量导出
    handleExport() {
      this.$modal.msgInfo('批量导出功能开发中...')
    },

    // 处理算法列表
    async handleAlgorithmList(row) {
      const templateId = row.id
      // 先加载当前模板的算法列表
      const algorithms = await this.loadTemplateAlgorithms(templateId)

      this.$refs.algorithmListDialog.open({
        algorithms: algorithms,
        success: (updatedAlgorithms) => {
          this.handleAlgorithmListSuccess(row, updatedAlgorithms)
        }
      })
    },

    // 加载模板关联的算法列表
    async loadTemplateAlgorithms(templateId) {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'template_operator_list',
            value1: templateId, // 模板ID
            pageSize: 100
          }
        })

        if (response.code === 200 && response.rows) {
          // 按排序序号排序并解析算法数据
          return response.rows
            .sort((a, b) => parseInt(a.value5 || 0) - parseInt(b.value5 || 0))
            .map(item => {
              try {
                return JSON.parse(item.value2) // 算法数据存储在value2中
              } catch (e) {
                console.error('解析算法数据失败:', e)
                return null
              }
            }).filter(Boolean)
        }
        return []
      } catch (error) {
        console.error('加载模板算法列表失败:', error)
        this.$message.error('加载算法列表失败')
        return []
      }
    },

    // 处理算法列表更新成功
    async handleAlgorithmListSuccess(template, algorithms) {
      try {
        // 先删除旧的关联数据
        await this.clearTemplateAlgorithms(template.id)

        // 保存新的关联数据
        for (let i = 0; i < algorithms.length; i++) {
          const algorithm = algorithms[i]
          await request({
            url: '/system/AutoOsmotic',
            method: 'post',
            data: {
              type: 'template_operator_list',
              value1: template.id, // 模板ID
              value2: JSON.stringify(algorithm), // 算法数据
              value3: algorithm.name, // 算法名称（便于查询）
              value4: algorithm.code, // 算法编码（便于查询）
              value5: i.toString() // 排序序号
            }
          })
        }

        // 更新模板的算法数量
        await request({
          url: '/system/AutoOsmotic',
          method: 'put',
          data: {
            ...template,
            value2: algorithms.length.toString() // 将算法数量存储在value2中
          }
        })

        // 保存算法列表到本地状态
        this.$set(this.templateAlgorithms, template.id, algorithms)
        this.$message.success('算法列表更新成功')

        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        console.error('保存算法列表失败:', error)
        this.$message.error('保存算法列表失败')
      }
    },

    // 清除模板的算法关联
    async clearTemplateAlgorithms(templateId) {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'template_operator_list',
            value1: templateId,
            pageSize: 100
          }
        })

        if (response.code === 200 && response.rows) {
          for (const item of response.rows) {
            await request({
              url: `/system/AutoOsmotic/${item.id}`,
              method: 'delete'
            })
          }
        }
      } catch (error) {
        console.error('清除模板算法关联失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.template-page {
  height: 100%;
}

.page-main {
  height: 100%;
}
</style>
